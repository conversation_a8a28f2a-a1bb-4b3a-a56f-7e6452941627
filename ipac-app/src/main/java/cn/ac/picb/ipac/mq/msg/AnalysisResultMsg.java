package cn.ac.picb.ipac.mq.msg;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 分析结果拷贝完成
 *
 * <AUTHOR>
 */
@Data
public class AnalysisResultMsg {

    @JsonProperty("task_id")
    private String taskId;

    @JsonProperty("denoisemethod")
    private String denoiseMethod;

    @JsonProperty("clustermethod")
    private String clusterMethod;

    @JsonProperty("adapter_5")
    private String adapter5;

    @JsonProperty("adapter_3")
    private String adapter3;

    @JsonProperty("taxonomymethod")
    private String taxonomyMethod;

    @JsonProperty("reference_database")
    private String referenceDatabase;

    @JsonProperty("group_column")
    private String groupColumn;

}

package cn.ac.picb.ipac.mq;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AnalysisError implements Serializable {

    private String taskId;

    @JsonProperty("error_code")
    private Integer errorCode;

    private String message;
}

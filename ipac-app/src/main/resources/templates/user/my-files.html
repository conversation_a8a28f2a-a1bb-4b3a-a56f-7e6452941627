<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
    <div class="wrapper py-0" layout:fragment="content">

        <div class="container-fluid">
            <div class="page-title-box py-3">
                <div class="row align-items-center">
                    <div class="col-sm-12">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a th:href="@{/home}" href="index.html"><i class="mdi mdi-home-outline"></i></a>
                            </li>
                            <li class="breadcrumb-item">Personal Center</li>
                            <li class="breadcrumb-item active">My Files</li>
                        </ol>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-2">
                    <div class="card card-box">
                        <div class="card-body">
                            <h4 class="mt-0 header-title mb-3"><i></i> Personal Center</h4>
                            <ul id="left-common">
                                <li class="py-1"><span class="mdi mdi-disc text-primary m-r-5"></span><a href="index-my-data.html" th:href="@{/usercenter/files}" class="text-secondary">My Files</a></li>
                                <li class="py-1"><span class="mdi mdi-disc text-muted m-r-5"></span><a class="text-secondary">My Analysis Tasks</a>
                                    <ul class="pt-1 pl-3">
                                        <li><a href="index-my-tasks.html" th:href="@{/usercenter/tasks}" class="text-secondary"><i class="mdi mdi-chevron-right"></i> IPP</a></li>
                                        <li><a th:href="@{/usercenter/venas/tasks}"  class="text-secondary"><i class="mdi mdi-chevron-right"></i> VENAS</a></li>
                                        <li><a th:href="@{/usercenter/gvap/tasks}"  class="text-secondary"><i class="mdi mdi-chevron-right"></i> GVAP</a></li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-10">
                    <div class="card card-box">
                        <div class="card-body">
                            <h4 class="mt-0 header-title mb-3"><i></i> My Files</h4>
                            <ol class="breadcrumb">
                                <li>Current File Path：
                                    <a href="javascript:void(0);" onclick="goto(this)" path="/">
                                        <i class="fa fa-home fa-lg"></i>
                                    </a>
                                </li>
                                <li th:each="path,idxState: ${currentPath}"  th:if="${idxState.index gt 0}">
                                    <a href="javascript:void(0);" onclick="goto(this)" th:path="${path.value}">
                                        [[${path.key}]]
                                    </a>
                                </li>
                            </ol>
                            <div class="info-box">
                                <p class="m-0 text-info">File Upload Instructions: Please use your username and password to log in to [[${@webConstant.FTP_PATH}]] to upload files.</p>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover table-striped table-nowrap table-task font-12" id="file_table">

                                    <thead>
                                        <tr>
                                       <!--     <th width="120"><input type="checkbox" onclick="selectAll(this)"></th>-->
                                            <th class="file_name">Name</th>
                                            <th width="120" class="file_size">Size</th>
                                            <th width="120"  class="file_type">Type</th>
                                            <th width="220">Last commit date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="item: ${data.files}">
                                           <!-- <td><input type="checkbox" name="filePath" th:value="${item.path}"></td>-->
                                            <td th:switch="${item.type}">
                                                <a th:case="D"
                                                   th:path="${data.currentPath eq '/' ? '' : data.currentPath}+'/'+${item.name}"
                                                   href="javascript:void(0);" onclick="goto(this)"><i class="fa fa-folder"></i> [[${item.name}]]</a>
                                                <span th:case="*">
                                    <th:block th:switch="${#strings.toLowerCase(T(cn.hutool.core.io.FileUtil).extName(item.name))}">
                                        <i th:case="zip" class="fa fa-file-zip-o"></i>
                                        <i th:case="rar" class="fa fa-file-zip-o"></i>
                                        <i th:case="tar" class="fa fa-file-zip-o"></i>
                                        <i th:case="jar" class="fa fa-file-zip-o"></i>
                                        <i th:case="xlsx" class="fa fa-file-excel-o-o"></i>
                                        <i th:case="xls" class="fa fa-file-excel-o"></i>
                                        <i th:case="txt" class="fa fa-file-text-o"></i>
                                        <i th:case="csv" class="fa fa-file-text-o"></i>
                                        <i th:case="png" class="fa fa-file-image-o"></i>
                                        <i th:case="jpg" class="fa fa-file-image-o"></i>
                                        <i th:case="pdf" class="fa fa-file-pdf-o-o"></i>
                                        <i th:case="word" class="fa fa-file-word-o"></i>
                                        <i th:case="*" class="fa fa-file-o"></i>
                                    </th:block>
                                    [[${item.name}]]
                                </span>
                                            </td>
                                            <td>
                                                <th:block th:if="${item.type eq 'F'}">[[${item.size}]]</th:block>
                                            </td>
                                            <td th:switch="${item.type}">
                                                <span th:case="F">File</span>
                                                <span th:case="D">Folder</span>
                                                <span th:case="H">Root</span>
                                                <span th:case="*">Unknown</span>
                                            </td>
                                            <!--<td th:text="${item.fileName}"></td>-->
                                            <td th:text="${#dates.format(item.lastModifyTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</html>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery.tablesorter.min.js}"></script>
    <script th:inline="none">
        $(document).ready(function() {
            $("#file_table").tablesorter({
                sortList: [[3,1],[1,0]]
            });
        });

        function goto (path) {
            var p = $(path).attr("path");
            console.info(p)
            if ($.trim(p) == '') {
                p = "/";
            }
            var _context_path = $("meta[name='_context_path']").attr("content");
            window.location.href = _context_path + "/usercenter/files?path=" + encodeURIComponent(p);
        }

        function selectAll (chk) {

            var filePaths = $(chk).parents("table").find("tbody").find("input[type=checkbox]");

            if (filePaths.length == 0) {
                return;
            }

            $.each(filePaths, function () {
                $(this).prop("checked", $(chk).prop("checked"));
            })
        }

        function batchDelFile() {

        }

        function deleteFile(id) {
            layer.confirm('<p class="text-center">Are you sure to delete it? Data cannot be retrieved after deletion</p>', {btn: ['confirm', 'cancel']}, function (){
                $.ajax({
                    url: "/usercenter/deleteFile",
                    data: {"id" : id},
                    dataType: 'json',
                    async : false,
                    method: 'post',
                    beforeSend: function () {
                        loadLayerIndex = layer.load(1, {
                            shade: [0.1, '#fff'] //0.1透明度的白色背景
                        });
                    },
                    success: function (result) {
                        if (result.code == 200) {
                            layer.msg("Delete successful！", {time: 500}, function () {
                                location.reload();
                            });
                        }else {
                            layer.alert(result.message, {icon: 2});
                        }
                    },
                    complete: function () {
                        layer.close(loadLayerIndex);
                    }
                });
            });
        }

    </script>
</th:block>

<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<div class="wrapper py-0" layout:fragment="content">
    <div class="main-box">
        <div class="dark-box">
            <section class="main-wrapper">
                <div class="main-content">
                    <div class="my-2">
                        <h3 class="text-center mb-2">
                            Platform Introduction
                        </h3>
                        <p class="mb-2">The integrated Pathogen Analysis Cloud (iPAC) platform is a comprehensive analysis platform for pathogen detection and analysis. The platform aims to provide comprehensive pathogen detection and genome evolution network services based on second-generation sequencing technology to address the challenges of disease transmission and traceability. Through the platform, users can upload and analyse the sequencing data of collected pathogen samples to quickly and accurately identify potential pathogens. At the same time, the platform is also capable of building a genome evolution network, providing a reference for intelligent real-time tracking of infection and transmission pathways. </p>
                        <p class="mb-2">In summary, the comprehensive pathogenic microbial analysis cloud platform provides an efficient and accurate tool for users such as research institutes, medical institutions and public health departments, helping to strengthen the monitoring, early warning and response capabilities of pathogenic microorganisms, and promoting scientific and intelligent disease prevention and control.</p>
                        <div class="row justify-content-center mb-1">
                            <div class="col-lg-10 col-xl-9">
                                <form action="" class="pt-1 pb-1">
                                    <div class="input-group home-search">
                                        <input type="text" class="form-control autocomplete" id="main-search"
                                               placeholder="Please enter the name of the pathogen">
                                        <div class="input-group-append">
                                            <button class="btn" type="button" id="button-addon2"><i
                                                    class="fa fa-fw fa-search"></i></button>
                                        </div>
                                    </div>
                                    <div style="padding-top: 5px">
                                        <div class="eg-info" style="text-align: left">
                                            <!-- <span class="d-inline-block text-right" style="width: 230px">e.g. 按照病毒物种分类号或者名称：</span> -->
                                            <span class="d-inline-block pl-4">e.g.</span>
                                            <a href="javascript:void(0);" title="2697049">2697049</a>
                                            <a href="javascript:void(0);" title="SARS-CoV-2">SARS-CoV-2</a>
                                        </div>
                                        <!-- <div class="eg-info" style="text-align: left">
                                            <span class="d-inline-block text-right" style="width: 230px">按照毒株名称模糊检索：</span>
                                            <a href="http://*************:20003/ViGTK/virusSearch/list?virusIdAndMore%5B0%5D=Bat" title="Bat">Bat</a>
                                            <a href="http://*************:20003/ViGTK/virusSearch/list?virusIdAndMore%5B0%5D=RaTG13" title="RaTG13">RaTG13</a>
                                        </div>

                                        <div class="eg-info" style="text-align: left">
                                            <span class="d-inline-block text-right" style="width: 230px">按照关键词检索：</span>
                                            <a href="http://*************:20003/ViGTK/virusSearch/list?key=SARS" title="SARS">SARS</a>
                                        </div>
                                        <div class="eg-info" style="text-align: left">
                                            <span class="d-inline-block text-right" style="width: 230px">按照宿主搜索：</span>
                                            <a href="http://*************:20003/ViGTK/virusSearch/list?host=Manis%20javanica" title="Manis javanica">Manis javanica</a>
                                            <a href="http://*************:20003/ViGTK/virusSearch/list?host=Rhinolophus%20affinis" title="Rhinolophus affinis">Rhinolophus affinis</a>
                                        </div>
                                        <div class="eg-info" style="text-align: left">
                                            <span class="d-inline-block text-right" style="width: 230px">序列覆盖度达到99.99以上：</span>
                                            <a href="http://*************:20003/ViGTK/virusSearch/list?taxonIdAndMore%5B0%5D=2697049&amp;isRecommend=Y&amp;validPercentStart=99.99" title="序列覆盖度达到99.99以上的SARS-CoV-2">SARS-CoV-2</a>
                                        </div> -->
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <div class="container-fluid">
                <h3 class="text-center my-4">Tools</h3>
                <div class="row justify-content-center mb-3">
                    <div class="col-lg-10">
                        <div class="card-deck card-tools">
                            <div class="card shadow-sm">
                                <img th:src="@{/images/pic-t04.jpg}" src="./assets/images/pic-t04.jpg" class="card-img-top" alt="...">
                                <div class="card-body">
                                    <h5 class="card-title">IPP</h5>
                                    <p class="card-text mb-1">Docking of next-generation sequencing data from host or environmental samples, combined with rapid identification of known pathogens and monitoring of microbial genome variation.</p>
                                    <p class="text-center mb-0">
                                        <a href="index-ipp.html" th:href="@{/ipp/main}" class="btn btn-primary btn-icon"><span class="btn-icon-label"><i class="mdi mdi-arrow-right mr-2"></i></span> View More</a>
                                    </p>
                                </div>
                            </div>

                            <div class="card shadow-sm">
                                <img th:src="@{/images/pic-t03.jpg}" src="./assets/images/pic-t03.jpg" class="card-img-top" alt="...">
                                <div class="card-body">
                                    <h5 class="card-title">VENAS2</h5>
                                    <p class="card-text mb-1">For the massive pathogenic microbial data, VENAS2 method can fast and accurate construct haplotype network for the pathogen through combining genomic variants with epidemiological information to ensure real-time genomic contact tracing.</p>
                                    <p class="text-center mb-0">
                                        <a  th:href="@{/venas/main1}"  href="javascript:void(0);" class="btn btn-primary btn-icon"><span class="btn-icon-label"><i class="mdi mdi-arrow-right mr-2"></i></span> View More</a>
                                    </p>
                                </div>
                            </div>

                            <div class="card shadow-sm">
                                <img th:src="@{/images/pic-t03.jpg}" src="./assets/images/pic-t03.jpg" class="card-img-top" alt="...">
                                <div class="card-body">
                                    <h5 class="card-title">GVAP</h5>
                                    <p class="card-text mb-1">A high-performance genomic variant analysis tool for bacterial and viral genome studies, detecting SNPs, InDels, and Structural Variants with comprehensive annotation reports.</p>
                                    <p class="text-center mb-0">
                                        <a th:href="@{/gvap/main}" href="javascript:void(0);" class="btn btn-primary btn-icon"><span class="btn-icon-label"><i class="mdi mdi-arrow-right mr-2"></i></span> View More</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</html>
<th:block layout:fragment="custom-script">
    <script>

        $('.virus-num').click(function () {
            if($(this).hasClass('active')) {
                $(this).removeClass('active')
                $(this).parents('.position-relative').removeClass('open')
            }else {
                $(this).addClass('active')
                $(this).parents('.position-relative').addClass('open')
            }
        })
        $('.virus-box-1 i.fa-times-circle').click(function () {
            $(this).parents('.virus-box-1').prev().find('.active').removeClass('active')
            $(this).parents('.position-relative').removeClass('open')
        })
        var availableTags = [
            "ActionScript",
            "AppleScript",
            "Asp",
            "BASIC",
            "C",
            "C++",
            "Clojure",
            "COBOL",
            "ColdFusion",
            "Erlang",
            "Fortran",
            "Groovy",
            "Haskell",
            "Java",
            "JavaScript",
            "Lisp",
            "Perl",
            "PHP",
            "Python",
            "Ruby",
            "Scala",
            "Scheme"
        ];
        $( ".autocomplete" ).autocomplete({
            source: availableTags
        });
        $(document).ready(function () {
            $(".eg-info a").click(function () {
                $("#main-search").val($(this).attr('title'));
            });
        })
    </script>
</th:block>

spring:
  thymeleaf:
    cache: false
    mode: HTML
    encoding: utf-8
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url:  *********************************************************************************************************************
    username: root
    password: Lfgzs@2021
  jpa:
    database: mysql
    show-sql: false
  data:
    mongodb:
      database: ipac
      host: ************
      port: 31944
      username: root
      password: vvLw4aRnx746YP
      authentication-database: admin
  rabbitmq:
    host: ************
    port: 30072
    username: ipac
    password: ipac
    virtual-host: /ipac
    publisher-returns: true
    publisher-confirm-type: correlated
    template:
      mandatory: true
    listener:
      simple:
        acknowledge-mode: manual
        concurrency: 5
        max-concurrency: 10
logging:
  config: classpath:config/logback-dev.xml
app:
  appName: ipac
  dataHome: /Users/<USER>/test/vic
  analysisResultHome: /Users/<USER>/test/vicAnaResult
baseUrl: http://local.biosino.org:8084
remotes:
  file-url: http://localhost:8080/file-service
biosino:
  cas-base-url: https://www.biosino.org/node-cas
  regist_url: https://www.biosino.org/bmdcRegist/register
  forget_pwd_url: https://www.biosino.org/bmdcRegist/forgetPwd
  change_pwd_url: https://www.biosino.org/bmdcRegist/changePwdPage
  ftp-path: sftp://fms.biosino.org:44399
venas:
  script-parent-path: ${app.dataHome}/venas
security:
  cas:
    server:
      base-url: ${biosino.cas-base-url}
      protocol-version: 2
    service:
      resolution-mode: dynamic
    authorization:
      mode: NONE
server:
  port: 8084

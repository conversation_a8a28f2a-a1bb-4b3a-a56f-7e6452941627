server:
  error:
    whitelabel:
      enabled: false
  servlet:
    session:
      timeout: 28800
spring:
  profiles:
    active: @spring.profiles.active@
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  mvc:
    throw-exception-if-no-handler-found: true
  servlet:
    multipart:
      enabled: true
      max-file-size: 600MB
      max-request-size: 600MB
ncov:
  rabbitmq:
    web:
      queueName: analysis-web-queue
    error:
      queueName: analysis-error-queue
    status:
      queueName: analysis-status-queue
venas1:
  rabbitmq:
    task:
      queueName: venas1-analysis-task-queue
venas2:
  rabbitmq:
    task:
      queueName: vena2s-analysis-task-queue
gvap:
  rabbitmq:
    task:
      queueName: gvap-analysis-task-queue
    result:
      queueName: gvap-analysis-result-queue
    error:
      queueName: gvap-analysis-error-queue
    status:
      queueName: gvap-analysis-status-queue
    web:
      queueName: gvap-analysis-web-queue
